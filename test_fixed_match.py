# -*- coding: utf-8 -*-
"""
测试修复后的图像匹配
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 套保续约自动化 import (
    load_template_image,
    find_image_on_screen,
    find_and_click_image
)
from PIL import Image

def test_fixed_matching():
    """
    测试修复后的图像匹配功能
    """
    print("="*60)
    print("测试修复后的图像匹配功能")
    print("="*60)
    
    # 1. 加载模板
    template_path = "产品设置.png"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    print("正在加载产品设置模板...")
    success = load_template_image(template_path, "产品设置")
    if not success:
        print("❌ 模板加载失败")
        return
    
    print("✅ 模板加载成功")
    
    # 2. 使用具体的截图文件进行测试
    screenshot_path = r"C:\Users\<USER>\Desktop\dm\screen_analysis_20250729.png"
    if not os.path.exists(screenshot_path):
        print(f"❌ 截图文件不存在: {screenshot_path}")
        return
    
    print(f"正在加载截图: {screenshot_path}")
    screenshot = Image.open(screenshot_path)
    print(f"✅ 截图加载成功，尺寸: {screenshot.size}")
    
    # 3. 测试不同阈值的匹配效果
    print("\n测试不同阈值的匹配效果:")
    thresholds = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]
    
    successful_matches = []
    
    for threshold in thresholds:
        print(f"\n测试阈值: {threshold}")
        match_result = find_image_on_screen("产品设置", screenshot, threshold)
        
        if match_result:
            x, y, w, h = match_result
            center_x, center_y = x + w // 2, y + h // 2
            successful_matches.append({
                'threshold': threshold,
                'position': (x, y),
                'center': (center_x, center_y),
                'size': (w, h)
            })
            print(f"✅ 匹配成功！")
        else:
            print(f"❌ 匹配失败")
    
    # 4. 显示结果
    print("\n" + "="*60)
    print("匹配测试结果:")
    print("="*60)
    
    if successful_matches:
        print(f"✅ 成功匹配 {len(successful_matches)} 次")
        
        # 显示第一个成功的匹配
        best_match = successful_matches[0]
        print(f"\n最佳匹配结果:")
        print(f"  阈值: {best_match['threshold']}")
        print(f"  位置: {best_match['position']}")
        print(f"  中心点: {best_match['center']}")
        print(f"  尺寸: {best_match['size']}")
        
        # 询问是否要测试点击功能
        print(f"\n是否要测试点击功能？")
        print("注意：这将在屏幕上执行实际的双击操作")
        user_input = input("输入 'y' 测试点击，其他键跳过: ")
        
        if user_input.lower() == 'y':
            print("正在测试点击功能...")
            # 使用最宽松的成功阈值
            test_threshold = successful_matches[-1]['threshold']
            success = find_and_click_image("产品设置", double_click=True, threshold=test_threshold)
            if success:
                print("✅ 点击测试成功！")
            else:
                print("❌ 点击测试失败")
        else:
            print("跳过点击测试")
    else:
        print("❌ 所有阈值都匹配失败")
        print("这可能表示还有其他问题需要解决")
    
    print("\n" + "="*60)
    print("测试完成！")
    
    # 5. 给出使用建议
    if successful_matches:
        recommended_threshold = successful_matches[-1]['threshold']  # 最宽松的成功阈值
        print(f"\n使用建议:")
        print(f"✅ 图像匹配功能已修复")
        print(f"✅ 建议使用阈值: {recommended_threshold}")
        print(f"✅ 可以在任务3中正常使用")
        
        print(f"\n代码使用示例:")
        print(f"load_template_image('产品设置.png', '产品设置')")
        print(f"find_and_click_image('产品设置', threshold={recommended_threshold})")
    else:
        print(f"\n需要进一步调试:")
        print(f"1. 检查模板图像是否正确")
        print(f"2. 确认截图中包含目标元素")
        print(f"3. 可能需要重新制作模板")

if __name__ == "__main__":
    test_fixed_matching()
