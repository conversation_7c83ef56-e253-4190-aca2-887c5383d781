# -*- coding: utf-8 -*-
"""
测试图像匹配功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 套保续约自动化 import (
    load_template_image,
    find_image_on_screen,
    find_and_click_image,
    capture_full_screen_screenshot,
    analyze_screen_with_image_matching
)

def test_image_matching():
    """
    测试图像匹配功能
    """
    print("="*60)
    print("图像匹配功能测试")
    print("="*60)
    
    # 检查是否有产品设置模板图像
    template_path = "产品设置.PNG"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        print("请确保 产品设置.PNG 文件在代码同一目录下")
        return False
    
    print(f"✅ 找到模板文件: {template_path}")
    
    # 1. 加载模板图像
    print("\n1. 加载模板图像...")
    success = load_template_image(template_path, "产品设置")
    if not success:
        print("❌ 模板加载失败")
        return False
    
    print("✅ 产品设置模板加载成功")
    
    # 2. 截取当前屏幕
    print("\n2. 截取当前屏幕...")
    screenshot = capture_full_screen_screenshot("test_screenshot.png")
    if not screenshot:
        print("❌ 截图失败")
        return False
    
    print(f"✅ 屏幕截图成功，尺寸: {screenshot.size}")
    print("   截图已保存为: test_screenshot.png")
    
    # 3. 查找图像匹配
    print("\n3. 查找图像匹配...")
    
    # 测试不同的阈值
    thresholds = [0.9, 0.8, 0.7, 0.6, 0.5]
    match_result = None
    
    for threshold in thresholds:
        print(f"\n   测试阈值: {threshold}")
        match_result = find_image_on_screen("产品设置", screenshot, threshold=threshold)
        
        if match_result:
            x, y, w, h = match_result
            center_x, center_y = x + w // 2, y + h // 2
            print(f"   ✅ 找到匹配！位置: ({x}, {y}), 尺寸: ({w}, {h})")
            print(f"      中心点: ({center_x}, {center_y})")
            break
        else:
            print(f"   ❌ 阈值 {threshold} 未找到匹配")
    
    if not match_result:
        print("\n❌ 所有阈值都未找到匹配")
        print("可能的原因:")
        print("1. 屏幕上没有显示产品设置界面")
        print("2. 模板图像与当前屏幕显示不匹配")
        print("3. 需要调整匹配阈值")
        return False
    
    # 4. 使用分析功能
    print("\n4. 使用屏幕分析功能...")
    result = analyze_screen_with_image_matching(["产品设置"], save_screenshot=True)
    
    if result:
        print(f"✅ 分析完成")
        print(f"   截图路径: {result['screenshot_path']}")
        print(f"   找到匹配项: {result['total_matches']} 个")
        
        if result['matches']:
            for i, match in enumerate(result['matches'], 1):
                print(f"   匹配 {i}: {match['template_name']}")
                print(f"      位置: {match['position']}")
                print(f"      中心点: {match['center']}")
        else:
            print("   未找到任何匹配")
    else:
        print("❌ 屏幕分析失败")
    
    # 5. 询问是否测试点击
    print("\n5. 测试点击功能...")
    user_input = input("是否测试点击功能？(y/N): ")
    
    if user_input.lower() == 'y':
        print("⚠️  注意：即将执行点击操作！")
        print("   请确保点击是安全的，不会造成意外操作")
        confirm = input("确认执行点击？(y/N): ")
        
        if confirm.lower() == 'y':
            print("正在执行点击...")
            success = find_and_click_image("产品设置", double_click=True, threshold=0.8)
            if success:
                print("✅ 点击操作成功")
            else:
                print("❌ 点击操作失败")
        else:
            print("取消点击操作")
    else:
        print("跳过点击测试")
    
    print("\n" + "="*60)
    print("测试完成！")
    return True

def test_dependencies():
    """
    测试依赖库是否正常
    """
    print("检查依赖库...")
    
    try:
        import cv2
        print(f"✅ OpenCV 版本: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV 未安装")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy 版本: {np.__version__}")
    except ImportError:
        print("❌ NumPy 未安装")
        return False
    
    try:
        from PIL import Image, ImageGrab
        print(f"✅ PIL/Pillow 可用")
    except ImportError:
        print("❌ PIL/Pillow 未安装")
        return False
    
    try:
        import pyautogui
        print(f"✅ PyAutoGUI 版本: {pyautogui.__version__}")
    except ImportError:
        print("❌ PyAutoGUI 未安装")
        return False
    
    return True

if __name__ == "__main__":
    print("图像匹配功能测试程序")
    print("="*60)
    
    # 首先检查依赖
    if not test_dependencies():
        print("\n❌ 依赖库检查失败，请安装缺失的库")
        sys.exit(1)
    
    print("\n✅ 所有依赖库检查通过")
    
    # 运行测试
    print("\n开始测试图像匹配功能...")
    time.sleep(2)  # 给用户时间准备屏幕
    
    success = test_image_matching()
    
    if success:
        print("\n🎉 图像匹配功能测试成功！")
    else:
        print("\n❌ 图像匹配功能测试失败")
        print("\n故障排除建议:")
        print("1. 确保屏幕上显示了包含'产品设置'的界面")
        print("2. 检查模板图像是否清晰且与当前显示匹配")
        print("3. 尝试重新截取模板图像")
        print("4. 调整匹配阈值")
